const { createAuditLog } = require("./auditLogController");
const { sendEmail } = require("../utils/emailService");
const { formatDateForReport } = require("../utils/dateUtils");
const db = require("../db/models");
const { Op } = require('sequelize');

// Log available models to help debug
console.log("Available models in accessFormController:", Object.keys(db));

// Define the UserAccessRequest model
const UserAccessRequest = db.UserAccessRequest;
// Approver Emails
const approverEmails = require("../config/approverEmails");

// Function to determine the appropriate branch head for premierkenya
const getBranchHeadForPremierKenya = (branch) => {
  if (!branch) return null;

  const branchLower = branch.toLowerCase();

  // Check if branch starts with "govt" or is "call center"
  if (branchLower.startsWith('govt') || branchLower.includes('call center')) {
    return 'HEAD_OF_CHECKOFF';
  }

  // Check if branch is "head office" or "karen"
  if (branchLower.includes('head office') || branchLower.includes('karen')) {
    return 'HEAD_OF_FINANCE';
  }

  // For all other branches
  return 'HEAD_OF_SME';
};

const dataMiddleware = async (req, res, next) => {
  const request = await UserAccessRequest.findByPk(req.params.id);
  if (!request) {
    return res.status(404).json({ error: "Access request not found" });
  }

  // Get the subsidiary from the x-subsidiary header or from the request object
  const subId = req.headers["x-subsidiary"]?.toLowerCase() || request.subId || "platinumkenya";

  console.log("Middleware processing for subsidiary:", subId);

  // Fetch approver emails based on the subId
  const emails = approverEmails[subId];

  if (!emails) {
    return res.status(400).json({
      error:
        "Invalid or missing emails configuration for the given subsidiary: " + subId,
    });
  }

  req.approverEmails = emails;

  const {
    firstName,
    lastName,
    email,
    systemName,
    branch,
    department,
    accessType,
    role,
    reason,
  } = request;

  req.auditData = {
    userName: firstName + " " + lastName,
    action: "Approval",
    formType: "access",
    formId: request.id,
    role,
    branch,
    systemName,
  };
  next();
};

// CREATE ACCESS REQUEST
const createAccessForm = async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      email,
      systemName,
      branch,
      department,
      telephone,
      accessType,
      role,
      reason,
    } = req.body;

    const subId = req.headers["x-subsidiary"];
    if (!subId) {
      return res.status(400).json({ error: "Missing x-subsidiary header" });
    }

    if (
      !firstName ||
      !lastName ||
      !email ||
      !systemName ||
      !branch ||
      !department ||
      !accessType ||
      !role
    ) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    // Check for duplicate phone number if telephone is provided
    if (telephone) {
      console.log(`Form submission - checking phone number GLOBALLY: ${telephone}`);

      const existingRequest = await UserAccessRequest.findOne({
        where: {
          telephone: telephone
          // Removed subId filter to check globally
        }
      });

      console.log(`Form submission - global search result:`, existingRequest ? {
        id: existingRequest.id,
        telephone: existingRequest.telephone,
        subId: existingRequest.subId,
        firstName: existingRequest.firstName,
        lastName: existingRequest.lastName,
        approvalStatus: existingRequest.approvalStatus
      } : 'No existing request found globally');

      if (existingRequest) {
        console.log(`Form submission blocked - duplicate phone number found globally: ${telephone} in subsidiary: ${existingRequest.subId}`);
        return res.status(400).json({
          error: "Phone number already exists",
          message: `The phone number ${telephone} is already registered in the system by ${existingRequest.firstName} ${existingRequest.lastName} in ${existingRequest.subId}.`
        });
      }

      console.log(`Form submission - phone number available globally: ${telephone}`);
    }

    const files = req.files || {};
    const attachments = [];

    for (const [key, fileList] of Object.entries(files)) {
      console.log(`Processing file list for key: ${key}`);
      console.log(`File list:`, fileList);
      
      for (const file of fileList) {
        console.log(`Processing file: ${file.originalname}, mimetype: ${file.mimetype}`);
        attachments.push({
          filename: file.originalname,
          contentType: file.mimetype,
          content: file.buffer.toString("base64"),
        });
      }
    }

    const accessForm = await UserAccessRequest.create({
      firstName,
      lastName,
      email,
      systemName,
      branch,
      department,
      telephone,
      accessType,
      role,
      reason,
      approvalStatus: "Pending",
      subId,
      attachments,
    });

    await createAuditLog(
      email,
      "Created Access Request",
      "access",
      accessForm.id
    );
    console.log("attachments", attachments);

    // Get the approver emails based on the subId
    const approvers = approverEmails[subId];
    if (!approvers) {
      return res.status(400).json({ error: "Invalid subsidiary" });
    }

    // Determine notification recipient based on subsidiary and branch
    let notificationRecipient;
    let recipientTitle;
    let successMessage;

    if (subId.toLowerCase() === 'premierkenya') {
      // For Premier Kenya, route based on branch
      const branchHead = getBranchHeadForPremierKenya(branch);

      if (branchHead && approvers[branchHead]) {
        notificationRecipient = approvers[branchHead];

        // Set recipient title based on branch head type
        switch (branchHead) {
          case 'HEAD_OF_CHECKOFF':
            recipientTitle = 'Head of Checkoff';
            break;
          case 'HEAD_OF_FINANCE':
            recipientTitle = 'Head of Finance';
            break;
          case 'HEAD_OF_SME':
            recipientTitle = 'Head of SME';
            break;
          default:
            recipientTitle = 'Branch Head';
        }

        successMessage = `Access form created successfully. Notification sent to ${recipientTitle}.`;
      } else {
        // Fallback to HR if branch head not found
        notificationRecipient = approvers.HR;
        recipientTitle = 'HR';
        successMessage = "Access form created successfully. Notification sent to HR.";
      }
    } else {
      // For other subsidiaries, send directly to HR (existing behavior)
      notificationRecipient = approvers.HR;
      recipientTitle = 'HR';
      successMessage = "Access form created successfully. Notification sent to HR.";
    }

    // Send email notification
    sendEmail({
      to: notificationRecipient,
      subject: "New Access Request Pending Approval",
      emailBody: `
        <p>Dear ${recipientTitle},</p>
        <p>A new access request has been submitted and is awaiting your approval.</p>
        <table style="border-collapse: collapse; width: auto;">
          <tr><td style="font-weight: bold; padding: 5px;">Requestor:</td><td style="padding: 5px;">${firstName} ${lastName}</td></tr>
          <tr><td style="font-weight: bold; padding: 5px;">Email:</td><td style="padding: 5px;"><a href="mailto:${email}">${email}</a></td></tr>
          <tr><td style="font-weight: bold; padding: 5px;">System Name:</td><td style="padding: 5px;">${systemName}</td></tr>
          <tr><td style="font-weight: bold; padding: 5px;">Branch:</td><td style="padding: 5px;">${branch}</td></tr>
          <tr><td style="font-weight: bold; padding: 5px;">Department:</td><td style="padding: 5px;">${department}</td></tr>
          <tr><td style="font-weight: bold; padding: 5px;">Access Type:</td><td style="padding: 5px;">${accessType}</td></tr>
          <tr><td style="font-weight: bold; padding: 5px;">Role:</td><td style="padding: 5px;">${role}</td></tr>
          <tr><td style="font-weight: bold; padding: 5px;">Reason:</td><td style="padding: 5px;">${reason || "N/A"}</td></tr>
        </table>
        <p><a href="https://uat-uap.platcorpgroup.com/user-access/ui/approval/${subId}" target="_blank">Click here to review and approve this request</a></p>
        <p>Best regards,<br>Premier Kenya Access Management System</p>`,
      tenant: subId,
      attachments: attachments.map(att => ({
        filename: att.filename,
        content: att.content,
        encoding: "base64",
        contentType: att.contentType,
      })),
    });

    return res.status(201).json({
      message: successMessage,
      data: accessForm,
    });
  } catch (error) {
    console.error("Error creating access form:", error);
    return res.status(500).json({
      error: "Failed to create access form",
      details: error.message,
    });
  }
};

// BRANCH HEAD APPROVAL (for premierkenya)
const approveBranchHead = async (req, res) => {
  try {
    console.log("Branch Head Approval process started for request ID:", req.params.id);
    console.log("Headers:", JSON.stringify(req.headers));

    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) {
      console.error("Request not found with ID:", req.params.id);
      return res.status(404).json({ error: "Access request not found" });
    }

    console.log("Current approval status:", request.approvalStatus);
    if (request.approvalStatus !== "Pending") {
      console.error("Invalid approval status:", request.approvalStatus);
      return res.status(400).json({
        error: "Approval cannot be processed.",
        message: "This request is not in 'Pending' status. Current status: " + request.approvalStatus
      });
    }

    // Update status to indicate branch head approval
    request.approvalStatus = "Approved by Branch Head";
    await request.save();
    console.log("Request status updated to:", request.approvalStatus);
  
    const {
      firstName,
      lastName,
      email,
      systemName,
      branch,
      department,
      accessType,
      role,
      reason,
      subId,
      attachments: storedAttachments,
    } = request;

    const attachments = (storedAttachments || []).map((file) => ({
      filename: file.filename,
      content: file.content,
      encoding: "base64",
      contentType: file.contentType,
    }));

    // Get the approver emails for the subsidiary
    const approvers = approverEmails[subId];

    // Send notification to HR for next level approval
    sendEmail({
      to: approvers.HR,
      subject: "Access Request Awaiting HR Approval",
      emailBody: `
        <p>Dear HR Team,</p>
        <p>The following access request has been approved by the Branch Head and requires your review.</p>
        <table style="border-collapse: collapse; width: auto;">
          <tr><td style="font-weight: bold; padding: 5px;">Requestor:</td><td style="padding: 5px;">${firstName} ${lastName}</td></tr>
          <tr><td style="font-weight: bold; padding: 5px;">Email:</td><td style="padding: 5px;"><a href="mailto:${email}">${email}</a></td></tr>
          <tr><td style="font-weight: bold; padding: 5px;">System Name:</td><td style="padding: 5px;">${systemName}</td></tr>
          <tr><td style="font-weight: bold; padding: 5px;">Branch:</td><td style="padding: 5px;">${branch}</td></tr>
          <tr><td style="font-weight: bold; padding: 5px;">Department:</td><td style="padding: 5px;">${department}</td></tr>
          <tr><td style="font-weight: bold; padding: 5px;">Access Type:</td><td style="padding: 5px;">${accessType}</td></tr>
          <tr><td style="font-weight: bold; padding: 5px;">Role:</td><td style="padding: 5px;">${role}</td></tr>
          <tr><td style="font-weight: bold; padding: 5px;">Reason:</td><td style="padding: 5px;">${reason || "N/A"}</td></tr>
        </table>
        <p><a href="https://uat-uap.platcorpgroup.com/user-access/ui/approval/${subId}" target="_blank">Click here to review and approve this request</a></p>
        <p>Best regards,<br>Premier Kenya Access Management System</p>`,
      tenant: subId,
      attachments,
    });

    return res.status(200).json({ message: "Branch Head Approval Completed", request });
  } catch (error) {
    console.error("Branch Head Approval Error:", error);
    return res
      .status(500)
      .json({ error: "Approval Error", details: error.message });
  }
};

// HR APPROVAL
const approveHR = async (req, res) => {
  try {
    console.log("HR Approval process started for request ID:", req.params.id);
    console.log("Headers:", JSON.stringify(req.headers));
    
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) {
      console.error("Request not found with ID:", req.params.id);
      return res.status(404).json({ error: "Access request not found" });
    }

    console.log("Current approval status:", request.approvalStatus);

    // For premierkenya, require branch head approval first
    const requestSubId = request.subId?.toLowerCase();
    if (requestSubId === 'premierkenya') {
      if (request.approvalStatus !== "Approved by Branch Head") {
        console.error("Invalid approval status for premierkenya:", request.approvalStatus);
        return res.status(400).json({
          error: "Approval cannot be processed.",
          message: "This request requires Branch Head approval first. Current status: " + request.approvalStatus
        });
      }
    } else {
      // For other subsidiaries, allow direct HR approval from Pending
      if (request.approvalStatus !== "Pending") {
        console.error("Invalid approval status:", request.approvalStatus);
        return res.status(400).json({
          error: "Approval cannot be processed.",
          message: "This request is not in 'Pending' status. Current status: " + request.approvalStatus
        });
      }
    }

    request.approvalStatus = "Approved by HR";
    await request.save();
    console.log("Request status updated to:", request.approvalStatus);

    const {
      firstName,
      lastName,
      email,
      systemName,
      branch,
      department,
      accessType,
      role,
      reason,
      subId,
      attachments: storedAttachments,
    } = request;

    const attachments = (storedAttachments || []).map((file) => ({
      filename: file.filename,
      content: file.content,
      encoding: "base64",
      contentType: file.contentType,
    }));
    console.log("attachments", attachments);

    // Get the approver emails for the subsidiary
    const approvers = approverEmails[subId];

    sendEmail({
      to: approvers.IT,
      subject: "Access Request Awaiting IT Approval",
      emailBody: `
        <p>Dear IT Team,</p>
        <p>The following access has been approved by HR and requires your review.</p>
        <table style="border-collapse: collapse;">
          <tr><td><strong>Requestor:</strong></td><td>${firstName} ${lastName}</td></tr>
          <tr><td><strong>Email:</strong></td><td>${email}</td></tr>
          <tr><td><strong>System:</strong></td><td>${systemName}</td></tr>
          <tr><td><strong>Branch:</strong></td><td>${branch}</td></tr>
          <tr><td><strong>Department:</strong></td><td>${department}</td></tr>
          <tr><td><strong>Access Type:</strong></td><td>${accessType}</td></tr>
          <tr><td><strong>Role:</strong></td><td>${role}</td></tr>
          <tr><td><strong>Reason:</strong></td><td>${reason || "N/A"}</td></tr>
        </table>
        <p><a href="https://uat-uap.platcorpgroup.com/user-access/ui/approval/${subId}" target="_blank">Click here to review and approve this request</a></p>`,
      tenant: subId,
      attachments,
    });

    return res.status(200).json({ message: "HR Approval Completed", request });
  } catch (error) {
    console.error("HR Approval Error:", error);
    return res
      .status(500)
      .json({ error: "Approval Error", details: error.message });
  }
};

// EXECUTIVE APPROVAL - DEPRECATED (Approval flow is now HR -> IT directly)
const approveExecutive = async (req, res) => {
  // This function is kept for backward compatibility but is no longer used in the approval flow
  console.warn("DEPRECATED: approveExecutive function called. The approval flow is now HR -> IT directly.");

  try {
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) {
      return res.status(404).json({ error: "Access request not found" });
    }

    // Return a message indicating this step is deprecated
    return res.status(400).json({
      error: "Executive approval step has been removed",
      message: "The approval flow has been updated to go directly from HR to IT. Please use the IT approval endpoint."
    });
  } catch (error) {
    console.error("Executive Approval Error:", error);
    return res
      .status(500)
      .json({ error: "Approval Error", details: error.message });
  }
};

// IT FINAL APPROVAL
const approveIT = async (req, res) => {
  try {
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) {
      return res.status(404).json({ error: "Access request not found" });
    }

    // For all subsidiaries, require HR approval before IT approval (EXECUTIVE step removed)
    if (request.approvalStatus !== "Approved by HR") {
      return res.status(400).json({ error: "HR Approval required before IT approval." });
    }

    request.approvalStatus = "Approved by IT";
    await request.save();

    // Log the approver email for audit purposes
    const approverEmail = req.user?.email || req.headers['x-approver-email'] || 'Unknown';
    console.log(`IT approval completed by: ${approverEmail} for request ID: ${request.id}`);

    return res.status(200).json({
      message: "IT Approval Completed",
      request,
      approvedBy: approverEmail
    });
  } catch (error) {
    console.error("IT Approval Error:", error);
    return res
      .status(500)
      .json({ error: "Approval Error", details: error.message });
  }
};

// GET ALL
const getAllAccessForms = async (req, res) => {
  try {
    const subId = req.headers["x-subsidiary"];

    // For premierfanikiwa subsidiary, we need to exclude middleName field
    // since the migration hasn't been applied yet
    let accessForms;
    if (subId === 'premierfanikiwa') {
      // Get all columns except middleName and other Premier Uganda specific fields
      accessForms = await UserAccessRequest.findAll({
        where: { subId },
        order: [["createdAt", "DESC"]],
        attributes: [
          'id', 'subId', 'systemName', 'branch', 'firstName', 'lastName',
          'email', 'telephone', 'department', 'accessType', 'role',
          'previousRole', 'reason', 'attachments', 'approvalStatus',
          'createdAt', 'updatedAt'
        ]
      });
    } else {
      // For other subsidiaries, get all fields
      accessForms = await UserAccessRequest.findAll({
        where: { subId },
        order: [["createdAt", "DESC"]],
      });
    }

    res.json({ data: accessForms });
  } catch (error) {
    console.error("Error fetching access forms:", error);
    res.status(500).json({ error: "Failed to fetch access forms" });
  }
};

// GET ONE
const getAccessFormById = async (req, res) => {
  try {
    const accessForm = await UserAccessRequest.findByPk(req.params.id);
    if (!accessForm)
      return res.status(404).json({ error: "Access form not found" });

    // For premierfanikiwa subsidiary, filter out Premier Uganda specific fields
    // since the migration hasn't been applied yet
    if (accessForm.subId === 'premierfanikiwa') {
      const filteredForm = {
        id: accessForm.id,
        subId: accessForm.subId,
        systemName: accessForm.systemName,
        branch: accessForm.branch,
        firstName: accessForm.firstName,
        lastName: accessForm.lastName,
        email: accessForm.email,
        telephone: accessForm.telephone,
        department: accessForm.department,
        accessType: accessForm.accessType,
        role: accessForm.role,
        previousRole: accessForm.previousRole,
        reason: accessForm.reason,
        attachments: accessForm.attachments,
        approvalStatus: accessForm.approvalStatus,
        createdAt: accessForm.createdAt,
        updatedAt: accessForm.updatedAt
      };
      res.status(200).json({ data: filteredForm });
    } else {
      res.status(200).json({ data: accessForm });
    }
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch access form" });
  }
};

// REJECT
const rejectAccessRequest = async (req, res) => {
  try {
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request)
      return res.status(404).json({ error: "Access request not found" });
    request.approvalStatus = "Rejected";
    await request.save();
    res
      .status(200)
      .json({ message: "Access request rejected successfully", request });
  } catch (error) {
    res.status(500).json({ error: "Failed to reject access request" });
  }
};

// GENERATE REPORT
const generateAccessRequestReport = async (req, res) => {
  try {
    const subId = req.headers["x-subsidiary"];
    if (!subId) {
      return res.status(400).json({ error: "Missing x-subsidiary header" });
    }

    // Get query parameters for filtering
    const {
      startDate,
      endDate,
      status,
      systemName,
      department,
      role
    } = req.query;

    // Build the where clause
    const whereClause = { subId };

    // Import Sequelize operators
    const { Op } = require('sequelize');

    // Add date range filter if provided
    if (startDate && endDate) {
      whereClause.createdAt = {
        [Op.between]: [
          new Date(startDate),
          new Date(endDate)
        ]
      };
    } else if (startDate) {
      whereClause.createdAt = {
        [Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      whereClause.createdAt = {
        [Op.lte]: new Date(endDate)
      };
    }

    console.log('Using Op from sequelize for date filtering');

    // Add status filter if provided
    if (status) {
      whereClause.approvalStatus = status;
    }

    // Add system name filter if provided
    if (systemName) {
      whereClause.systemName = systemName;
    }

    // Add department filter if provided
    if (department) {
      whereClause.department = department;
    }

    // Add role filter if provided
    if (role) {
      whereClause.role = role;
    }

    console.log('Executing report query with where clause:', JSON.stringify(whereClause, null, 2));

    // Get all access requests based on filters
    const accessRequests = await UserAccessRequest.findAll({
      where: whereClause,
      order: [["createdAt", "DESC"]],
      attributes: {
        exclude: ['attachments'] // Exclude attachments to reduce payload size
      }
    });

    console.log(`Found ${accessRequests.length} access requests matching criteria`);

    // Format the data for CSV
    const formattedData = accessRequests.map(request => {
      const data = request.get({ plain: true });

      // Format the accessType array to a string
      if (Array.isArray(data.accessType)) {
        data.accessType = data.accessType.join(', ');
      }

      // Format dates with correct timezone for the subsidiary
      data.createdAt = formatDateForReport(data.createdAt, subId);
      data.updatedAt = formatDateForReport(data.updatedAt, subId);

      return data;
    });

    // Return the data
    return res.status(200).json({
      message: "Access request report generated successfully",
      data: formattedData,
      count: formattedData.length,
      filters: {
        subId,
        startDate: startDate || 'All',
        endDate: endDate || 'All',
        status: status || 'All',
        systemName: systemName || 'All',
        department: department || 'All',
        role: role || 'All'
      }
    });
  } catch (error) {
    console.error("Error generating access request report:", error);
    console.error("Error stack:", error.stack);

    // Check if it's a Sequelize error
    if (error.name === 'SequelizeConnectionError') {
      return res.status(500).json({
        error: "Database connection error",
        details: "Could not connect to the database. Please try again later."
      });
    }

    // Check if it's a validation error
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        error: "Validation error",
        details: error.message
      });
    }

    return res.status(500).json({
      error: "Failed to generate access request report",
      details: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

// VALIDATE PHONE NUMBER
const validatePhoneNumber = async (req, res) => {
  try {
    const { telephone } = req.query;
    const subId = req.headers["x-subsidiary"];

    console.log(` Phone validation request:`, {
      telephone,
      subId,
      rawQuery: req.query,
      headers: req.headers
    });

    if (!telephone) {
      return res.status(400).json({ error: "Phone number is required" });
    }

    if (!subId) {
      return res.status(400).json({ error: "Missing x-subsidiary header" });
    }

    // Check if phone number already exists GLOBALLY (across all subsidiaries)
    // Phone numbers should be unique across the entire system
    console.log(`Searching for existing phone number GLOBALLY: ${telephone}`);

    const existingRequest = await UserAccessRequest.findOne({
      where: {
        telephone: telephone
        // Removed subId filter to check globally
      }
    });

    console.log(`Global search result:`, existingRequest ? {
      id: existingRequest.id,
      telephone: existingRequest.telephone,
      subId: existingRequest.subId,
      firstName: existingRequest.firstName,
      lastName: existingRequest.lastName,
      approvalStatus: existingRequest.approvalStatus
    } : 'No existing request found globally');

    if (existingRequest) {
      console.log(`Duplicate phone number found globally: ${telephone} in subsidiary: ${existingRequest.subId}`);
      return res.status(200).json({
        exists: true,
        message: `Phone number ${telephone} is already registered in the system.`,
        existingSubsidiary: existingRequest.subId,
        existingUser: `${existingRequest.firstName} ${existingRequest.lastName}`
      });
    }

    console.log(` Phone number available globally: ${telephone}`);
    return res.status(200).json({
      exists: false,
      message: "Phone number is available."
    });
  } catch (error) {
    console.error("Error validating phone number:", error);
    return res.status(500).json({
      error: "Failed to validate phone number",
      details: error.message
    });
  }
};

module.exports = {
  createAccessForm,
  approveBranchHead,
  approveHR,
  approveExecutive,
  approveIT,
  getAllAccessForms,
  getAccessFormById,
  rejectAccessRequest,
  dataMiddleware,
  generateAccessRequestReport,
  validatePhoneNumber
};
