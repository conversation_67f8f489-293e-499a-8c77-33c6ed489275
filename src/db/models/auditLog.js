'use strict';
const { Model, DataTypes, Sequelize } = require('sequelize');
module.exports = (sequelize) => {
  class AuditLog extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }

  AuditLog.init(
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      userName: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      action: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      formType: {
        type: DataTypes.ENUM('access', 'revocation', 'approval', 'user-management'),
        allowNull: false,
      },
      formId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      subId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      systemName: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      branch: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      role: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      approvers: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      ip: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      path: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      method: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      agent: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      timestamp: {
        type: DataTypes.DATE,
        defaultValue: Sequelize.NOW,
      },
    },
    {
      sequelize,
      modelName: 'AuditLog',
      tableName: 'audit_logs',
      timestamps: true,
      createdAt: 'createdat',
      updatedAt: 'updatedat',
      hooks: {
        beforeDestroy: () => {
          throw new Error('Audit logs cannot be deleted.');
        },
        beforeUpdate: () => {
          throw new Error('Audit logs cannot be modified.');
        },
      },
    }
  );

  return AuditLog;
};
